import * as React from "react";
import { Check<PERSON><PERSON>, ChevronsUpDown } from "lucide-react";
import { Button } from "~/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { useState } from "react";
import { api } from "~/trpc/react";
import { Input } from "~/components/ui/input";
import { useDebounce } from "~/hooks/useDebounce";
import { Command, CommandEmpty, CommandList } from "~/components/ui/command";
import { Loading } from "./Loading";
import { Tag } from "./Tag";
import type { Sport } from "~/lib/enums";
import type { VideoUpdateInput } from "~/server/api/routers/video";
import { Label } from "~/components/ui/label";
import { cn } from "~/lib/utils";

export const AthleteSelect = ({
  sport,
  selectedAthletes = [],
  setSelectedAthletes,
  label,
  disabled,
}: {
  sport: Sport;
  selectedAthletes: VideoUpdateInput["athletes"];
  setSelectedAthletes: (selectedAthletes: VideoUpdateInput["athletes"]) => void;
  label?: string;
  disabled?: boolean;
}) => {
  const [open, setOpen] = useState(false);
  const [searchText, setSearchText] = useState("");
  const debounceSearchText = useDebounce<string>(searchText, 300);

  const { data: athletes, isFetching } = api.pta.getAthletes.useQuery(
    {
      name: debounceSearchText,
      sport,
    },
    {
      enabled: debounceSearchText.length >= 3,
    },
  );

  const handleAddOrRemoveAthlete = (
    athlete_id: string,
    name: string,
    isHp: boolean,
  ) => {
    const index = selectedAthletes?.findIndex(
      (athlete) => athlete.athleteId === athlete_id,
    );
    if (index > -1) {
      setSelectedAthletes(
        selectedAthletes.filter((athlete) => athlete.athleteId !== athlete_id),
      );
    } else {
      setSelectedAthletes([
        ...selectedAthletes,
        { athleteId: athlete_id, name, isHp },
      ]);
    }
    setOpen(false);
  };

  const athleteError = !Array.isArray(athletes);

  return (
    <div className="grid gap-3">
      <Label>{label ?? "Select Athlete"}</Label>
      <Popover open={open} onOpenChange={setOpen} disabled={disabled}>
        <PopoverTrigger asChild disabled={disabled}>
          <div
            className={cn(
              "flex w-full flex-wrap items-center gap-1 truncate rounded-none border-b border-primary px-2 py-2",
              !disabled && "cursor-pointer",
              disabled && "cursor-not-allowed opacity-50",
            )}
          >
            {selectedAthletes.map((athlete) => (
              <Tag
                key={athlete.athleteId}
                label={athlete.name ?? "unknown"}
                onClose={
                  disabled
                    ? undefined
                    : () =>
                        handleAddOrRemoveAthlete(
                          athlete.athleteId,
                          athlete.name ?? "unknown",
                          athlete.isHp,
                        )
                }
              />
            ))}
            <ChevronsUpDown className="ml-auto h-4 w-4 shrink-0 opacity-50" />
          </div>
        </PopoverTrigger>
        <PopoverContent className=" w-96 p-0">
          <Input
            value={searchText}
            placeholder="Search Athletes..."
            onChange={(e) => setSearchText(e.target.value)}
          />
          <Command>
            <CommandList className="relative">
              {isFetching && (
                <div className="flex h-12 w-full items-center justify-center">
                  <Loading />
                </div>
              )}
              {!athleteError &&
                athletes?.map((athlete) => {
                  const athlete_full_name = `${athlete.first_name} ${athlete.last_name}`;
                  return (
                    <div
                      key={athlete.athlete_id}
                      className="flex w-full items-center justify-center"
                    >
                      <Button
                        variant="ghost"
                        key={athlete.athlete_id}
                        className="w-full items-center justify-center"
                        onClick={() =>
                          handleAddOrRemoveAthlete(
                            athlete.athlete_id,
                            athlete_full_name,
                            athlete.is_hp,
                          )
                        }
                      >
                        <div className="absolute left-0">
                          {selectedAthletes.some(
                            (selected) =>
                              selected.athleteId === athlete.athlete_id,
                          ) && <CheckIcon className="ml-4 h-4 w-4" />}
                        </div>
                        <div className="w-full text-center">
                          {athlete_full_name}
                        </div>
                      </Button>
                    </div>
                  );
                })}
              {searchText.length < 3 && (
                <CommandEmpty>
                  Search for athletes by typing at least 3 characters.
                </CommandEmpty>
              )}
              {!athletes && !isFetching && searchText.length >= 3 && (
                <CommandEmpty>No athlete found.</CommandEmpty>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
};
