import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import type { Sport } from "~/lib/enums";
import { api } from "~/trpc/react";
import { Loading } from "./Loading";
import Divider from "./Divider";
import { But<PERSON> } from "~/components/ui/button";
import { CheckIcon, ChevronsUpDown } from "lucide-react";
import { Tag } from "./Tag";
import { Label } from "~/components/ui/label";
import { cn } from "~/lib/utils";

export const Metadata = ({
  label,
  sport,
  selectedTags = [],
  Trigger,
  onTagClick,
  disabled,
}: {
  label?: string;
  sport: Sport;
  selectedTags: string[];
  Trigger?: React.ReactNode;
  onTagClick: (tag: string) => void;
  disabled?: boolean;
}) => {
  const { data, isLoading } = api.pta.getSportMetadata.useQuery({ sport });

  return (
    <div className="grid gap-3">
      {!Trigger && <Label>{label ?? "Select Metadata"}</Label>}
      <Popover disabled={disabled}>
        {!Trigger && (
          <PopoverTrigger asChild disabled={disabled}>
            <div
              className={cn(
                "flex w-full flex-wrap items-center gap-1 truncate rounded-none border-b border-primary px-2 py-2",
                !disabled && "cursor-pointer",
                disabled && "cursor-not-allowed opacity-50",
              )}
            >
              {selectedTags?.map((tag) => (
                <Tag
                  key={tag}
                  label={tag}
                  onClose={disabled ? undefined : () => onTagClick(tag)}
                />
              ))}
              <ChevronsUpDown className="ml-auto h-4 w-4 shrink-0 opacity-50" />
            </div>
          </PopoverTrigger>
        )}
        {Trigger && (
          <PopoverTrigger asChild disabled={disabled}>
            {Trigger}
          </PopoverTrigger>
        )}
        <PopoverContent className="w-full">
          <div className="w-80">
            <p className="text-lg font-semibold">Select Metadata</p>
            <div className="mt-4">
              {isLoading && <Loading />}
              {data && (
                <div className="max-h-96 overflow-y-auto">
                  {Object.entries(data).map(([key, values]) => (
                    <div key={key}>
                      <Divider text={key} />
                      <div className="flex flex-wrap">
                        {values.map((value) => (
                          <Button
                            variant="ghost"
                            key={value}
                            onClick={() => onTagClick(value)}
                          >
                            <div className="w-4">
                              {selectedTags && selectedTags.includes(value) && (
                                <CheckIcon className="h-4 w-4" />
                              )}
                            </div>
                            {value}
                          </Button>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};
