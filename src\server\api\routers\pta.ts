import { z } from "zod";
import { sports } from "~/lib/enums";
import { formatSport } from "~/lib/utils";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import {
  getAccessToken,
  getAthletes,
  getAthletesByIds,
  getCompetitions,
  getSportMetadata,
} from "~/server/utils/pta";

export const ptaRouter = createTRPCRouter({
  getAthletes: protectedProcedure
    .input(
      z.object({
        name: z.string().min(3),
        sport: z.enum(sports).optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const accessToken = await getAccessToken(ctx);

      const athletes = await getAthletes({
        token: accessToken,
        sport: formatSport(input.sport),
        name: input.name,
      });

      return athletes;
    }),
  /**
   * @deprecated no need to get athlete names from pta since they are stored in db
   */
  getAthletesByIds: protectedProcedure
    .input(
      z.object({
        ids: z.array(z.string()),
      }),
    )
    .query(async ({ ctx, input }) => {
      const accessToken = ctx.session.accessToken;
      console.log(accessToken);
      const athletes = await getAthletesByIds({
        token: accessToken,
        ids: input.ids,
      });
      return athletes;
    }),
  getSportMetadata: protectedProcedure
    .input(
      z.object({
        sport: z.enum(sports),
      }),
    )
    .query(async ({ ctx, input }) => {
      const accessToken = await getAccessToken(ctx);
      const metadata = await getSportMetadata({
        token: accessToken,
        sport: input.sport,
      });
      return metadata;
    }),
  getCompetitions: protectedProcedure
    .input(
      z.object({
        sport: z.enum(sports),
      }),
    )
    .query(async ({ ctx, input }) => {
      const accessToken = await getAccessToken(ctx);
      const competitions = await getCompetitions({
        token: accessToken,
        sport: formatSport(input.sport)!,
      });
      return competitions;
    }),
});
