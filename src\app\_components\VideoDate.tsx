import { format } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";

import { Calendar } from "~/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { cn } from "~/lib/utils";

export function VideoDate({
  selected,
  onSelect,
  disabled,
}: {
  selected?: Date;
  onSelect: (date?: Date) => void;
  disabled?: boolean;
}) {
  return (
    <Popover disabled={disabled}>
      <PopoverTrigger asChild disabled={disabled}>
        <button
          className={cn(
            "flex w-full items-center justify-start border-b border-white pb-2 text-left font-normal",
            !selected && "text-muted-foreground",
            disabled && "cursor-not-allowed opacity-50",
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {selected ? format(selected, "PPP") : <span>Pick a date</span>}
        </button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
        <Calendar
          mode="single"
          selected={selected}
          onSelect={disabled ? undefined : onSelect}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
}
