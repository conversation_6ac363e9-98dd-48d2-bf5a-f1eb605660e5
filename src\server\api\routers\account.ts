import {
  type SQLWrapper,
  and,
  eq,
  inArray,
  like,
  count,
  or,
  asc,
  desc,
  isNotNull,
} from "drizzle-orm";
import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import {
  competition,
  videoAthletes,
  videoTags,
  videos,
} from "~/server/db/schema";
import type { RouterOutputs } from "../root";
import {
  type UserRole,
  sports,
  videoPermissions,
  videoStatuses,
  sortBy,
  SortBy,
} from "~/lib/enums";
import { refreshMUXToken } from "~/server/utils/mux";
import { TRPCError } from "@trpc/server";
import { getUserSports } from "~/lib/roles";
import { getServiceToken } from "~/server/utils/keycloak";
import { getAthletes, getAthletesAttr } from "~/server/utils/pta";
import type { DBAthleteWithName } from "~/lib/interface";

type Output = RouterOutputs["account"];

export type AccountVideosOutput = Output["videos"];
export type AccountVideoOutput = Output["videoById"];

export const accountRouter = createTRPCRouter({
  videos: protectedProcedure
    .input(
      z.object({
        page: z.number().default(0),
        pageSize: z.number().default(10),
        searchText: z.string().optional(),
        filter: z.object({
          status: z.array(z.enum(videoStatuses)).optional(),
          sport: z.array(z.enum(sports)).optional(),
          permission: z.array(z.enum(videoPermissions)).optional(),
          isDraft: z.boolean().optional(),
          createdBy: z.array(z.string()).optional(),
          metadata: z.array(z.string()).optional(),
          sortBy: z
            .enum([...sortBy, "name_desc", "video_date_asc"])
            .optional()
            .default(SortBy.video_date),
        }),
      }),
    )
    .query(async ({ ctx, input }) => {
      const andCondition: (SQLWrapper | undefined)[] = [
        eq(videos.isDeleted, false),
      ];
      const isAdmin = ctx.session.user.roles.includes("admin");
      const isAnalyst = ctx.session.user.roles.includes("analyst");

      if (isAnalyst || isAdmin) {
        //analyst can view videos in his sports
        const analystSports = getUserSports(
          ctx.session.user.roles as UserRole[],
        );
        if (analystSports.length > 0) {
          andCondition.push(inArray(videos.sport, analystSports));
        } else {
          andCondition.push(eq(videos.createdById, ctx.session.user.id));
        }
      } else {
        andCondition.push(eq(videos.createdById, ctx.session.user.id));
      }

      const { searchText, page, pageSize, filter } = input;
      const { status, sport, permission, isDraft, metadata, sortBy } = filter;

      if (status) {
        andCondition.push(inArray(videos.status, status));
      }
      if (sport) {
        andCondition.push(inArray(videos.sport, sport));
      }
      if (metadata !== undefined && metadata.length > 0) {
        andCondition.push(
          or(
            // Direct match: parent video has the metadata
            inArray(
              videos.id,
              ctx.db
                .select({ id: videoTags.videoId })
                .from(videoTags)
                .where(inArray(videoTags.text, metadata)),
            ),
            // Indirect match: parent video's children have the metadata
            inArray(
              videos.id,
              ctx.db
                .select({ parentId: videos.parentId })
                .from(videos)
                .innerJoin(videoTags, eq(videos.id, videoTags.videoId))
                .where(
                  and(
                    inArray(videoTags.text, metadata),
                    isNotNull(videos.parentId),
                  ),
                ),
            ),
          ),
        );
      }
      if (permission) {
        andCondition.push(inArray(videos.permission, permission));
      }
      if (isDraft !== undefined) {
        andCondition.push(eq(videos.isDraft, isDraft));
      }
      if (searchText && searchText.length > 0) {
        const serviceToken = await getServiceToken();
        const searchedAthletes = await getAthletes({
          token: serviceToken.access_token,
          name: searchText,
        });
        let searchedAthleteIds: string[] = [];
        if (searchedAthletes) {
          searchedAthleteIds = searchedAthletes.map((x) => x.athlete_id);
        }
        andCondition.push(
          or(
            like(videos.title, `%${searchText}%`),
            like(videos.sport, `%${searchText}%`),
            like(videos.status, `%${searchText}%`),
            inArray(
              videos.competitionId,
              ctx.db
                .select({ id: competition.id })
                .from(competition)
                .where(like(competition.name, `%${searchText}%`)),
            ),
            or(
              // Direct match: parent video has matching tags
              inArray(
                videos.id,
                ctx.db
                  .select({ id: videoTags.videoId })
                  .from(videoTags)
                  .where(like(videoTags.text, `%${searchText}%`)),
              ),
              // Indirect match: parent video's children have matching tags
              inArray(
                videos.id,
                ctx.db
                  .select({ parentId: videos.parentId })
                  .from(videos)
                  .innerJoin(videoTags, eq(videos.id, videoTags.videoId))
                  .where(
                    and(
                      like(videoTags.text, `%${searchText}%`),
                      isNotNull(videos.parentId),
                    ),
                  ),
              ),
            ),
          ),
        );
        if (searchedAthleteIds.length > 0) {
          andCondition.push(
            inArray(
              videos.id,
              ctx.db
                .select({ id: videoAthletes.videoId })
                .from(videoAthletes)
                .where(inArray(videoAthletes.athleteId, searchedAthleteIds)),
            ),
          );
        }
      }

      const offset = Math.max(0, page) * pageSize;

      const sortByMap = {
        name: [
          asc(videos.title),
          (desc(videos.videoDate), desc(videos.createdAt)),
        ], //sort by title first, then videoDate
        name_desc: [
          desc(videos.title),
          (desc(videos.videoDate), desc(videos.createdAt)),
        ], //sort by title first, then videoDate
        video_date: (desc(videos.videoDate), desc(videos.createdAt)), //sort by videoDate, if null, sort by createdAt
        video_date_asc: (asc(videos.videoDate), asc(videos.createdAt)),
      };

      const videosCount = await ctx.db
        .select({ count: count() })
        .from(videos)
        .where(and(...andCondition));

      const videoList = await ctx.db.query.videos.findMany({
        where: and(...andCondition),
        orderBy: sortByMap[sortBy],
        limit: pageSize,
        offset,
        with: {
          user: true,
          tags: true,
        },
      });

      return { videosCount, videoList };
    }),
  videoById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const isAdmin = ctx.session.user.roles.includes("admin");
      const isAnalyst = ctx.session.user.roles.includes("analyst");

      const andCondition = [
        eq(videos.id, input.id),
        eq(videos.isDeleted, false),
      ];
      if (!isAdmin) {
        if (isAnalyst) {
          //analyst can view videos in his sports
          const analystSports = getUserSports(
            ctx.session.user.roles as UserRole[],
          );
          if (analystSports.length > 0) {
            andCondition.push(inArray(videos.sport, analystSports));
          } else {
            andCondition.push(eq(videos.createdById, ctx.session.user.id));
          }
        } else {
          andCondition.push(eq(videos.createdById, ctx.session.user.id));
        }
      }

      const video = await ctx.db.query.videos.findFirst({
        where: and(...andCondition),
        with: {
          athletes: true,
          tags: true,
          competition: true,
        },
      });
      if (!video) {
        throw new TRPCError({ code: "NOT_FOUND", message: "Video not found" });
      }

      let playbackToken = undefined;
      let thumbnailToken = undefined;
      let storyboardToken = undefined;

      if (video.muxPlaybackId) {
        const refreshedPlaybackToken = await refreshMUXToken({
          aud: "video",
          muxPlaybackId: video.muxPlaybackId,
          oldToken: video.playbackToken,
        });
        const refreshedThumbnailToken = await refreshMUXToken({
          aud: "thumbnail",
          muxPlaybackId: video.muxPlaybackId,
          oldToken: video.thumbnailToken,
          thumbnailUrl: video.thumbnail,
        });
        const refreshedStoryboardToken = await refreshMUXToken({
          aud: "storyboard",
          muxPlaybackId: video.muxPlaybackId,
          oldToken: video.storyboardToken,
        });
        playbackToken =
          refreshedPlaybackToken.newToken ?? refreshedPlaybackToken.oldToken;
        thumbnailToken =
          refreshedThumbnailToken.newToken ?? refreshedThumbnailToken.oldToken;
        storyboardToken =
          refreshedStoryboardToken.newToken ??
          refreshedStoryboardToken.oldToken;

        if (
          !!refreshedPlaybackToken.newToken ||
          !!refreshedThumbnailToken.newToken ||
          refreshedStoryboardToken.newToken
        ) {
          await ctx.db
            .update(videos)
            .set({ playbackToken, thumbnailToken, storyboardToken })
            .where(eq(videos.id, video.id));
        }
      }

      video.playbackToken = playbackToken!;
      video.thumbnailToken = thumbnailToken!;
      video.storyboardToken = storyboardToken!;

      const athletesWithAttr = await getAthletesAttr(video.athletes);

      video.athletes = athletesWithAttr;

      type VideoOutput = typeof video;
      interface Res extends VideoOutput {
        athletes: DBAthleteWithName[];
      }

      return video as Res;
    }),
});
